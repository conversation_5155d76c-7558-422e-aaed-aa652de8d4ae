import { sampleSize } from 'lodash';
import assert from 'node:assert/strict';
import { autoInjectable, delay, inject } from 'tsyringe';

import { IPlatform, IReview } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, PostedStatus } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import * as platformsScrapper from ':microservices/platforms-scrapper';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PlatformHeaderConfigOptions, replyToReview } from ':modules/providers/use-cases';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import {
    extractBusinessId,
    TripadvisorReply,
    TripadvisorReplyPayload,
    TripadvisorReview,
} from ':modules/reviews/platforms/tripadvisor/tripadvisor-review-mapper';
import { ReviewMapper } from ':modules/reviews/reviews.mapper';
import { reviewsReplyCounter } from ':modules/reviews/reviews.metrics';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { PlatformReplyPayload, PlatformReviewsUseCases, ReviewInputWithRestaurantAndPlatformIds } from ':modules/reviews/reviews.types';
import { SlackChannel, SlackService } from ':services/slack.service';

export const MAX_TRIPADVISOR_REPLY_RETRIES = 20;

@autoInjectable()
export default class TripadvisorReviewsUseCases implements PlatformReviewsUseCases {
    constructor(
        @inject(delay(() => ReviewsRepository)) private readonly reviewsRepository: ReviewsRepository,
        @inject(SlackService) private readonly _slackService: SlackService,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async getReviewsData({ restaurantId }: { restaurantId?: string }, recentOnly?: boolean) {
        assert(restaurantId, 'Missing restaurantId');
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.TRIPADVISOR);

        if (!platform) {
            return { error: true, message: MalouErrorCode.PLATFORM_NOT_FOUND };
        }
        const validUrlRegexes = [
            /https:\/\/www\.tripadvisor\.[a-z]{2,3}\/Restaurant_Review-/,
            /https:\/\/www\.tripadvisor\.[a-z]{2,3}\/Hotel_Review-/,
            /https:\/\/www\.tripadvisor\.[a-z]{2,3}\/Attraction_Review-/, // Used for activities such as spa, etc.
        ];
        if (!platform.socialLink || validUrlRegexes.every((regex) => !platform.socialLink?.match(regex))) {
            return { error: true, message: MalouErrorCode.REVIEW_INCORRECT_SOCIAL_LINK, errorData: platform.socialLink };
        }
        const params = {
            endpoint: platform.socialLink.replace(/https:\/\/www\.tripadvisor\.[a-z]{2,3}/, ''),
            recentOnly: !!recentOnly,
        };

        await platformsScrapper.reviews(PlatformKey.TRIPADVISOR, restaurantId.toString(), params, false);
        return { sentToSQS: true, socialId: platform.socialId };
    }

    mapReviewsDataToMalou = function (platform: IPlatform, reviewsData): ReviewInputWithRestaurantAndPlatformIds[] {
        return reviewsData.map((review) => ReviewMapper.mapToMalouReview(platform, review));
    };

    /**
     * Fetch TripAdvisor reviews directly using axios (pulled from lambda scraper)
     * @param socialId - The TripAdvisor location ID
     * @param recentOnly - Whether to fetch only recent reviews (limits offset)
     * @param useGraphQl2 - Whether to use the newer GraphQL query format
     * @returns Parsed reviews in Malou format
     */
    async getReviewsData2({
        socialId,
        recentOnly = false,
        useGraphQl2 = false,
    }: {
        socialId: string;
        recentOnly?: boolean;
        useGraphQl2?: boolean;
    }): Promise<TripadvisorReview[]> {
        assert(socialId, 'Missing socialId');

        const DEFAULT_REVIEW_LIMIT = 20;
        const maxOffset = recentOnly ? 60 : 2500;
        const reviewsPerPage = DEFAULT_REVIEW_LIMIT;

        const promises = [];

        // Create requests for different offsets to get more reviews
        for (let offset = 0; offset <= maxOffset; offset += reviewsPerPage) {
            if (useGraphQl2) {
                promises.push(this._crawlRestaurantReviewsWithGraphQl2(socialId, offset, reviewsPerPage));
            } else {
                promises.push(this._crawlRestaurantReviewsWithAxios(socialId, offset, reviewsPerPage));
            }
        }

        const allResults = await Promise.allSettled(promises);
        const validResults = allResults.filter((r) => r.status === 'fulfilled').map((r) => r.value);
        const failedResults = allResults.filter((r) => r.status === 'rejected').map((r) => r.reason);

        if (validResults.length === 0) {
            logger.warn('[FAILED_TRIP_REVIEWS] No valid results there is an issue with the fetch', { failedResults, socialId });
            throw new Error('No valid results there is an issue with the fetch');
        }

        if (failedResults.length) {
            logger.error('[FAILED_TRIP_REVIEWS] Some requests failed', { failedResults, socialId });
        }

        // Extract reviews from responses
        const rawReviewList = [];
        for (const result of validResults) {
            const rawReviews = result?.data?.ReviewsProxy_getReviewListPageForLocation?.[0]?.reviews;
            if (rawReviews?.length) {
                rawReviewList.push(...rawReviews);
            }
        }

        // Parse reviews to Malou format
        const parsedReviews = rawReviewList?.flat()?.map((r) => ({
            id: r.id,
            title: r.title,
            text: r.text,
            date: r.publishedDate,
            rating: r.rating,
            lang: r.originalLanguage,
            endpoint: r.url,
            answered: r.mgmtResponse?.id
                ? {
                      date: r.mgmtResponse?.publishedDate,
                      answer: r.mgmtResponse?.text,
                      author: {
                          name: r.mgmtResponse?.username,
                      },
                  }
                : false,
            profileName: r.userProfile?.displayName || r.username,
            mediaUrls: [],
        }));

        return parsedReviews || [];
    }

    /**
     * Update Reviews in malou database with new review reply
     * @param {Object} comment
     * @param {Object} comment.review
     * @param {string} comment.comment
     */

    async reply({
        review,
        comment,
        headerConfig,
        retryReplyingToOtherReviews = true,
    }: {
        review: IReview;
        comment: PlatformReplyPayload;
        headerConfig?: PlatformHeaderConfigOptions;
        retryReplyingToOtherReviews?: boolean;
    }): Promise<TripadvisorReply & ({} | { error: any; review: IReview })> {
        const tripadvisorComment = comment as TripadvisorReplyPayload;
        let locationId: number | null = null;
        try {
            assert(review.socialLink, 'Missing socialLink on review');
            locationId = extractBusinessId(review.socialLink);
            await replyToReview(PlatformKey.TRIPADVISOR, { review, comment, locationId }, { headerConfig });
            reviewsReplyCounter.add(1, {
                source: PlatformKey.TRIPADVISOR,
                status: 'success',
            });
        } catch (error) {
            logger.warn('[ERROR_REPLY_REVIEW_TRIPADVISOR] Error when replying to review, set comment to RETRY', {
                review,
                comment,
                locationId,
                error,
            });
            return { comment: tripadvisorComment.comment, posted: PostedStatus.RETRY, error, review };
        }
        if (retryReplyingToOtherReviews) {
            await this.retryReplyingSomeReviews(headerConfig);
        }
        return { comment: tripadvisorComment.comment, posted: PostedStatus.PENDING };
    }

    pushReviewComment = ({ socialId, key, comment }) => this.reviewsRepository.updateUniqueReviewComment({ socialId, key, comment });

    updateComment = async function () {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.TRIPADVISOR,
            },
        });
    };

    async fetchTotalReviewCount(_restaurantId: string): Promise<number> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'TripadvisorReviewsUseCases does not implement fetchTotalReviewCount',
        });
    }

    retryReplyingSomeReviews = async (headerConfig?: PlatformHeaderConfigOptions, reviewsSampleSize = 10): Promise<void> => {
        const reviews = await this.reviewsRepository.getReviewsWithCommentsToRetryReplying(PlatformKey.TRIPADVISOR);
        if (!reviews.length) {
            return;
        }
        const reviewsSample = sampleSize(reviews, reviewsSampleSize);
        const promises = reviewsSample.map(async (review) => this._updateReviewComment(review, headerConfig)).filter(Boolean);
        const results = await Promise.all(promises);
        if (results.length) {
            logger.info(`[RETRY_REPLYING_REVIEWS_TRIPADVISOR] ${results.length} reviews updated`, { results });
        }
        return;
    };

    private _updateReviewComment = async (review: IReview, headerConfig?: PlatformHeaderConfigOptions): Promise<IReview | undefined> => {
        const commentToRetryPosting = review.comments.find((comment) => comment.posted === PostedStatus.RETRY);
        if (!commentToRetryPosting) {
            return;
        }
        const result = await this.reply({
            review,
            comment: { comment: commentToRetryPosting.text },
            headerConfig,
            retryReplyingToOtherReviews: false,
        });
        const currentRetries = commentToRetryPosting.retries ?? 0;
        const retries = result.posted === PostedStatus.RETRY ? currentRetries + 1 : currentRetries;
        const posted = retries > MAX_TRIPADVISOR_REPLY_RETRIES ? PostedStatus.REJECTED : result.posted;

        if (result.posted === PostedStatus.RETRY && posted === PostedStatus.REJECTED) {
            const { name } = await this._restaurantsRepository.findOneOrFail({
                filter: { _id: review.restaurantId },
                options: { lean: true },
                projection: { name: 1 },
            });
            this._slackService.sendAlert({
                channel: SlackChannel.REVIEWS_ALERTS,
                data: {
                    err: new MalouError(MalouErrorCode.PLATFORM_PUBLISH_ERROR, {
                        message: 'Could not publish reply to Tripadvisor review',
                        metadata: { socialId: review.socialId },
                    }),
                    endpoint: `restaurants/${review.restaurantId.toString()}/reputation/reviews?reviewId=${review._id.toString()}`,
                    metadata: {
                        description: `Reply could not be published to Tripadvisor for review with 
                        socialId ${review.socialId}`,
                        restaurantName: name,
                    },
                },
            });
            reviewsReplyCounter.add(1, {
                source: PlatformKey.TRIPADVISOR,
                status: 'failure',
            });
        }

        return this.reviewsRepository.updateReviewCommentStatus({
            commentId: commentToRetryPosting._id,
            posted,
            retries,
        });
    };

    /**
     * Extract social ID from TripAdvisor endpoint (similar to lambda utility)
     */
    private _extractSocialIdFromEndpoint(endpoint: string): string {
        return endpoint.split('-')[2].replace('d', '');
    }

    /**
     * Crawl TripAdvisor reviews using axios (original approach from lambda)
     */
    private async _crawlRestaurantReviewsWithAxios(socialId: string, offset: number, limit: number = 20) {
        const data = JSON.stringify({
            variables: {
                locationId: Number(socialId),
                filters: [],
                limit: limit,
                offset: offset,
                sortType: null,
                sortBy: 'SERVER_DETERMINED',
            },
            extensions: {
                preRegisteredQueryId: '9365c2244f5b46a6',
            },
        });

        const config = {
            method: 'post' as const,
            maxBodyLength: Infinity,
            url: 'https://www.tripadvisor.fr/data/graphql/ids',
            headers: {
                'Content-Type': 'application/json',
                Accept: '*/*',
                'Accept-Language': 'fr-FR,fr;q=0.9',
                'User-Agent':
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15',
                Cookie: 'TAUnique=%1%enc%3ulédo;',
            },
            data: data,
        };

        return axios
            .request(config)
            .then((response) => {
                logger.info('[TRIPADVISOR_REVIEWS_AXIOS] Response received', { socialId, offset, limit });
                return response.data;
            })
            .catch((error) => {
                logger.error('[TRIPADVISOR_REVIEWS_AXIOS] Error fetching reviews', { socialId, offset, limit, error });
                throw error;
            });
    }

    /**
     * Crawl TripAdvisor reviews using GraphQL2 format (newer approach from lambda)
     */
    private async _crawlRestaurantReviewsWithGraphQl2(socialId: string, offset: number, limit: number = 20) {
        const data = JSON.stringify({
            variables: {
                locationId: Number(socialId),
                offset: offset,
                limit: limit,
                keywordVariant: 'location_keywords_v2_llr_order_30_fr',
                needKeywords: false,
                language: 'fr',
                filters: [
                    {
                        axis: 'SORT',
                        selections: ['mostRecent'],
                    },
                ],
                prefs: {
                    showMT: false,
                    sortBy: 'DATE',
                    sortType: '',
                },
                initialPrefs: {
                    showMT: false,
                    sortBy: 'DATE',
                    sortType: '',
                },
            },
            extensions: {
                preRegisteredQueryId: 'aaff0337570ed0aa',
            },
        });

        const config = {
            method: 'post' as const,
            maxBodyLength: Infinity,
            url: 'https://www.tripadvisor.fr/data/graphql/ids',
            headers: {
                'Content-Type': 'application/json',
                Accept: '*/*',
                'Accept-Language': 'fr-FR,fr;q=0.9',
                'User-Agent':
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15',
                Cookie: 'TAUnique=%1%enc%3ulédo;',
            },
            data: data,
        };

        return axios
            .request(config)
            .then((response) => {
                logger.info('[TRIPADVISOR_REVIEWS_GRAPHQL2] Response received', { socialId, offset, limit });
                return response.data;
            })
            .catch((error) => {
                logger.error('[TRIPADVISOR_REVIEWS_GRAPHQL2] Error fetching reviews', { socialId, offset, limit, error });
                throw error;
            });
    }
}
