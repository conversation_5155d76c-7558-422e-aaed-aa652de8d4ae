// TRIP/reviews.js

require('aws-sdk');
const { extractSocialIdFromEndpoint, log } = require('../../utils');
const { crawlRestaurantReviewsWithAxios, crawlReviewOriginalText, DEFAULT_REVIEW_LIMIT } = require('./provider');
const { sendMessageToSQS } = require('../../../sqs');
const { uploadJsonFile } = require('../../../s3');
const { v4: uuidv4 } = require('uuid');


/**
 * 
 * @param {string} socialId 
 * @param {boolean} recentOnly 
 */
const getReviewsDataUsingGraphQlApi = async (socialId, recentOnly) => {
    const randomId = Math.floor(Math.random() * 99999).toString();
    console.time(randomId);
    log(randomId, `$$$ Event for socialId: ${socialId} received At ${new Date().toISOString()}`);
    const maxOffset = recentOnly ? 20 : 2500;
    const promises = [];
    const reviewsPerPage = DEFAULT_REVIEW_LIMIT;
    for (let offset = 0; offset <= maxOffset; offset += reviewsPerPage) {
        promises.push(crawlRestaurantReviewsWithAxios(socialId, offset, randomId));
    }
    const allResults = (await Promise.allSettled(promises));
    const validResults = allResults.filter((r) => r.status === 'fulfilled').map((r) => r.value);
    const failedResults = allResults.filter((r) => r.status === 'rejected').map((r) => r.reason);
    if (validResults.length === 0) {
        console.warn('[FAILED_TRIP_REVIEWS] No valid results there is an issue with the fetch', { failedResults, socialId });
        throw new Error('No valid results there is an issue with the fetch');
    }
    if (failedResults.length) {
        console.error('[FAILED_TRIP_REVIEWS] Some requests failed', { failedResults, socialId });
    }
    const rawReviewList = [];
    for (const result of validResults) {
        // const rawReviews = result?.data?.locations?.[0]?.reviewListPage?.reviews
        const rawReviews = result?.data?.ReviewsProxy_getReviewListPageForLocation?.[0]?.reviews;
        if (rawReviews?.length) {
            rawReviewList.push(...rawReviews);
        }
    }

    const parsedReviews = rawReviewList?.flat()?.map((r) => ({
        id: r.id,
        title: r.title,
        text: r.text,
        date: r.publishedDate,
        rating: r.rating,
        lang: r.originalLanguage,
        endpoint: r.url,
        answered: r.mgmtResponse?.id ? {
            date: r.mgmtResponse?.publishedDate,
            answer: r.mgmtResponse?.text,
            author: {
                name: r.mgmtResponse?.username
            }
        } : false,
        profileName: r.userProfile?.displayName || r.username,
        mediaUrls: []
    }));
    console.timeEnd(randomId);
    return parsedReviews;
};

const main = async (params, pageType, restaurantId, platformKey) => {
    console.log('[TRIP_RESTAURANT_REVIEWS] Starting to fetch reviews', { params, restaurantId, platformKey });
    const { endpoint, recentOnly } = params;
    const socialId = extractSocialIdFromEndpoint(endpoint);
    try {
        const reviews = await getReviewsDataUsingGraphQlApi(socialId, recentOnly);
        console.log('[TRIP_RESTAURANT_REVIEWS] Reviews received v2', { reviewsParsed: reviews.map(review => review.id), reviews, restaurantId, platformKey });
        if (reviews?.length) {
            console.log('[TRIP_RESTAURANT_REVIEWS] Retrieved some reviews', { restaurantId, platformKey });
            const path = _createPath(restaurantId);
            const uploaded = await _uploadReviewsToS3(path, reviews);
            return sendMessageToSQS({ data: { file: uploaded, url: path } }, pageType, restaurantId, platformKey).catch(() => { });
        } else {
            console.log('[TRIP_RESTAURANT_REVIEWS] No reviews retrieved but everything worked fine', { restaurantId, platformKey });
        }
    } catch (error) {
        console.error('[TRIP_RESTAURANT_REVIEWS] Error trying to fetch reviews', { error, restaurantId, platformKey });
        return sendMessageToSQS({ error: 'tripadvisor_review_fetch_failed' }, pageType, restaurantId, platformKey).catch(() => { });
    }
};

module.exports = {
    main
};


const _uploadReviewsToS3 = async (path, reviews) => {
    try {
        console.info('[RESTAURANT] Uploading reviews to s3', { path, reviewsNumber: reviews.length });
        return uploadJsonFile(path, JSON.stringify(reviews));
    } catch (error) {
        console.error('[RESTAURANT] Error trying to upload reviews to s3', { path, error });
        throw error;
    }
};

const _createPath = (restaurantId) => {
    const id = uuidv4();
    return `reviews/${restaurantId}/${id}`;
};



// TRIP/provider.js


/* eslint-disable complexity */
/* eslint-disable radix */
require('aws-sdk');
const { crawlPage, buildReviewGraphQlHeaders } = require('../../utils');
const axios = require('axios');

const DEFAULT_REVIEW_LIMIT = 20;
/**
 * @param {string} socialId
 * @deprecated use _buildReviewsBodyForGraphQl2
 */
const _buildReviewsBodyForGraphQl = (socialId, offset = 0, limit = DEFAULT_REVIEW_LIMIT) => ([
    {
        variables: {
            locationId: Number(socialId),
            pageOffset: offset,
            pageSize: limit,
            targetedReviewId: null,
            dateFilter: {
                minTime: ""
            },
            filters: [
                {
                    axis: 'PROVIDER',
                    selections: ['TA'],
                }, // Only tripadvisor reviews
                {
                    axis: 'RESPONSE_STATUS',
                    selections: ['NONE'],
                }, // Only reviews without response
            ],
        },
        extensions: {
            preRegisteredQueryId: "d95bc0f3f8495b9b"
        }
    }
]);

/**
 * 
 * @param {string|number} socialId 
 * @param {number} offset 
 * @param {number} limit 
 * @returns 
 */
const _buildReviewsBodyForGraphQl2 = (socialId, offset = 0, limit = DEFAULT_REVIEW_LIMIT) => ({
    "variables": {
        "locationId": Number(socialId),
        "offset": offset,
        "limit": limit,
        "keywordVariant": "location_keywords_v2_llr_order_30_fr",
        "needKeywords": false,
        "language": "fr",
        "filters": [
            {
                "axis": "SORT",
                "selections": [
                    "mostRecent"
                ]
            }
        ],
        "prefs": {
            "showMT": false,
            "sortBy": "DATE",
            "sortType": ""
        },
        "initialPrefs": {
            "showMT": false,
            "sortBy": "DATE",
            "sortType": ""
        },
    },
    "extensions": {
        "preRegisteredQueryId": "aaff0337570ed0aa"
    }
});
/**
 * 
 * @param {string|number} socialId 
 * @param {number} offset 
 * @param {number} limit 
 * @returns 
 */
const _buildReviewsBodyForGraphQl3 = (socialId, offset = 0, limit = DEFAULT_REVIEW_LIMIT) => ({
    "variables": {
        "locationId": 695212,
        "filters": [],
        "limit": 15,
        "offset": 15,
        "sortType": null,
        "sortBy": "DATE"
    },
    "extensions": {
        "preRegisteredQueryId": "9365c2244f5b46a6"
    }
});

/**
 * 
 * @param {Array<number>} reviewSocialIds 
 * @returns 
 */
const _buildReviewOriginalTextBody = (reviewSocialIds) => ({
    "variables": {
        "reviewsIds": reviewSocialIds,
    },
    "extensions": {
        "preRegisteredQueryId": "55ab9595a36e6548"
    }
});

const crawlRestaurantReviews = async (socialId, offset, randomId) => crawlPage({
    method: 'POST',
    url: 'https://www.tripadvisor.fr/data/graphql/ids',
    headers: buildReviewGraphQlHeaders(5),
    body: _buildReviewsBodyForGraphQl3(socialId, offset),
    shouldWork: data => !!data?.data?.locations,
}, 2, 2, randomId);

const crawlRestaurantReviewsWithAxios = async ({ socialId, offset, limit = DEFAULT_REVIEW_LIMIT }) => {
    const data = JSON.stringify({
        "variables": {
            "locationId": Number(socialId),
            "filters": [],
            "limit": limit,
            "offset": 0,
            "sortType": null,
            "sortBy": "SERVER_DETERMINED"
        },
        "extensions": {
            "preRegisteredQueryId": "9365c2244f5b46a6"
        }
    });

    const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://www.tripadvisor.fr/data/graphql/ids',
        headers: {
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Accept-Language': 'fr-FR,fr;q=0.9',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15',
            'Cookie': 'TAUnique=%1%enc%3ulédo;'
            // 'Cookie': 'jojoe'
        },
        data: data
    };

    return axios.request(config)
        .then((response) => {
            console.log(JSON.stringify(response.data));
            return response.data;
        })
        .catch((error) => {
            console.log(error);
            throw error;
        });


};

const crawlReviewOriginalText = async (socialIds, randomId) => crawlPage({
    method: 'POST',
    url: 'https://www.tripadvisor.fr/data/graphql/ids',
    headers: buildReviewGraphQlHeaders(5),
    body: _buildReviewOriginalTextBody(socialIds),
    shouldWork: data => !!data?.data?.ReviewsProxy_getBestLanguageMatchReviewById,
}, 2, 2, randomId);

module.exports = {
    DEFAULT_REVIEW_LIMIT,
    crawlRestaurantReviews,
    crawlReviewOriginalText,
    crawlRestaurantReviewsWithAxios
};


// utils.js (not everything is relevant)

const config = require('../configuration');
const { spawn } = require('child_process');
const axios = require('axios');
const AWS = require('aws-sdk');
/**
 *
 * @param {String} platformName the plaform name
 * @param {String} pageType search or place for example
 * @param {Object} params the params sent by the request
 */
const checkMandatoryParams = (platformName, pageType, params) =>
    config.platforms[platformName][pageType].mandatoryParams.every(
        (param) => params[param] !== undefined && params[param] !== ""
    );

/**
 * Makes an HTTP request based on the provided parameters.
 *
 * @param {Object} data - The configuration for the HTTP request.
 * @param {string} data.method - The HTTP method to use (e.g., 'GET', 'POST').
 * @param {string} data.url - The URL to which the request is sent.
 * @param {Object|Object[]} data.headers - The headers to be sent with the request. Can be a single header object or an array of header objects.
 * @param {Object} [data.body] - The body of the request, used with methods like 'POST'.
 * @param {Function} data.shouldWork - A function to validate the response (e.g., to ensure there is no captcha).
 * @returns {Promise} - A Promise that resolves with the response of the HTTP request.
 */
const crawlPage = (
    {
        url,
        headers = {},
        shouldWork = () => true,
        method = "GET",
        body = {},
        timeout = 3000,
    },
    retries = 3,
    crawlersAmt = 5,
    randomId
) =>
    fetchUntilWorks(
        {
            method,
            url,
            headers,
            body,
            timeout,
        },
        shouldWork,
        retries,
        crawlersAmt,
        randomId
    );

// Will not work with node v10+ on aws lambda
const crawlPageBashGet = async (url, headers) => {
    let headersLine = "";
    for (const [key, value] of Object.entries(headers)) {
        headersLine += `-H \'${key}: ${value}\' `;
    }
    let html = "";
    const promise = new Promise(function (resolve, reject) {
        const child = spawn("/bin/sh", [
            "-c",
            `curl -v --location --request GET \'${url}\' ${headersLine}`,
        ]);
        child.stdout.on("data", (data) => {
            html += String(data);
        });
        child.stderr.on("data", (_data) => {
            // console.log(String(data))
        });
        child.stdout.on("close", () => {
            resolve(html);
        });
        child.stdout.on("error", (error) => {
            reject(error);
        });
    });
    return promise;

    // child.stderr.on('data', function () {
    //     console.log('ON STDERR')
    //     console.log(arguments)
    // })
    // child.stdout.on('close', () => {
    //     console.log('done!')
    // })
    // console.log('child :', child);
    // const promise = new Promise(function (resolve, reject) {

    //     // curl not installed on lambda node v > 10
    //     // could use maybe spawn https://stackoverflow.com/questions/58956951/curl-on-aws-lambda-gives-command-not-found-error
    //     exec(`curl -v --location --request GET \'${url}\' ${headersLine}`, { maxBuffer: 1024 * 600 }, function (error, stdout, stderr) {
    //         console.info('stdout :', stdout)
    //         console.info('stderr :', stderr)
    //         if (error !== null) {
    //             reject(error)
    //         } else {
    //             console.info('stderr :', stderr)
    //             if (stderr.match(/HTTP\/2 403/)) {
    //                 reject(platformsFields.SPOTTED_FORBIDDEN)
    //             }
    //             if (stderr.match(/HTTP\/2 404/)) {
    //                 reject(platformsFields.SPOTTED_FORBIDDEN)
    //             }
    //             if (!stderr.match(/HTTP\/2 200/)) {
    //                 reject(platformsFields.STATUS_NOT_OK)
    //             }
    //             resolve({ data: stdout, meta: stderr })
    //         }
    //     })
    // })
    // return promise
};

const callPuppeteerService = (code) => {
    AWS.config.update({
        region: process.env.AWS_REGION,
        accessKeyId: process.env.AWS_KEY,
        secretAccessKey: process.env.AWS_SECRET,
    });

    const lambda = new AWS.Lambda({ apiVersion: "latest" });
    return new Promise((resolve, reject) => {
        const params = {
            FunctionName:
                "arn:aws:lambda:eu-west-3:515192135070:function:puppeteerService", // the lambda function we are going to invoke
            InvocationType: "RequestResponse",
            LogType: "Tail",
            Payload: JSON.stringify({
                eventName: "runCode",
                authorization: process.env.PUPP_AUTH,
                code: code,
                incomingParams: null,
            }),
        };

        lambda.invoke(params, function (err, data) {
            if (err) {
                console.error("err :>> ", err);
                reject(err);
            } else {
                resolve(data.Payload);
            }
        });
    });
};

/**
 *
 * @param {String} html
 */
const cleanHtml = (html) => {
    if (!html) {
        return null;
    }
    return html.replace(/\r/g, "").replace(/\n/g, "").replace(/\s\s+/g, " ");
};

/**
 * @param {string} a
 * @param {string} b
 * @return {number}
 */
const levenshteinDistance = (a, b) => {
    const distanceMatrix = Array(b.length + 1)
        .fill(null)
        .map(() => Array(a.length + 1).fill(null));

    for (let i = 0; i <= a.length; i += 1) {
        distanceMatrix[0][i] = i;
    }

    for (let j = 0; j <= b.length; j += 1) {
        distanceMatrix[j][0] = j;
    }

    for (let j = 1; j <= b.length; j += 1) {
        for (let i = 1; i <= a.length; i += 1) {
            const indicator = a[i - 1] === b[j - 1] ? 0 : 1;
            distanceMatrix[j][i] = Math.min(
                distanceMatrix[j][i - 1] + 1, // deletion
                distanceMatrix[j - 1][i] + 1, // insertion
                distanceMatrix[j - 1][i - 1] + indicator // substitution
            );
        }
    }

    return distanceMatrix[b.length][a.length];
};

const reverseString = (str) =>
    str === "" ? "" : reverseString(str.substr(1)) + str.charAt(0);

const randomString = (length) =>
    Math.round(36 ** (length + 1) - (Math.random() * 36) ** length)
        .toString(36)
        .slice(1);

/**
 *
 * @param {String} url
 * @returns {String}
 */
const buildProxyUrl = (url) =>
    config.proxy.baseUrl +
    process.env.PROXY_TOKEN +
    config.proxy.queryParams +
    encodeURIComponent(url);

/**
 *
 * @param {string} endpoint
 * @returns {string}
 */
const extractSocialIdFromEndpoint = (endpoint) => {
    return endpoint.split("-")[2].replace("d", "");
};

/**
 *
 * @param {number} count
 * @returns {Array<Object>}
 */
const buildReviewGraphQlHeaders = (count) => {
    const userAgents = [
        "Mozilla/5.0 (Linux; Android 13; SM-S901B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 13; SM-S901U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 13; SM-S908B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 13; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 13; SM-A515F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 10; VOG-L29) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        "Mozilla/5.0 (iPhone14,3; U; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/602.1.50 (KHTML, like Gecko) Version/10.0 Mobile/19A346 Safari/602.1",
        "Mozilla/5.0 (iPhone12,1; U; CPU iPhone OS 13_0 like Mac OS X) AppleWebKit/602.1.50 (KHTML, like Gecko) Version/10.0 Mobile/15E148 Safari/602.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) FxiOS/13.2b11866 Mobile/16A366 Safari/605.1.15",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A5370a Safari/604.1",
        "Mozilla/5.0 (iPhone9,3; U; CPU iPhone OS 10_0_1 like Mac OS X) AppleWebKit/602.1.50 (KHTML, like Gecko) Version/10.0 Mobile/14A403 Safari/602.1",
        "Mozilla/5.0 (X11; CrOS x86_64 8172.45.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.64 Safari/537.36",
        "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.2526.111 Safari/537.36",
    ];
    const startingIndex = Math.floor(Math.random() * userAgents.length);
    const headers = Array.from(Array(count).keys()).map((i) => ({
        Accept: "*/*",
        // "accept-language": "fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7",
        "accept-language": "fr-FR,fr;q=0.9",
        "Content-type": "application/json",
        // "sec-fetch-dest": "empty", // weirdly important
        // "sec-fetch-mode": "same-origin", // weirdly important
        // "sec-fetch-site": "same-origin", // weirdly important
        // "User-Agent": userAgents[(startingIndex + i) % userAgents.length],
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15",
        "Cookie": "TAUnique=%1%enc%3AkDtKR7Hn7E1oHx5CWZ7W%2B%2FOfoud7BugLM8MALF%2BD8yjtoFX%2ByFvQXaO26oWDatyGNox8JbUSTxk%3D; __vt=ot3oUUd1aFF-ZVXoABQCT24E-H_BQo6gx1APGQJPtzfethxZ3DRdUtJKF_upQBZB0Ygz_KWSjfi8DbR5ZfjVAEdqguZEh_c5UkbxkZipacitIdlkigKPa_gCMmmF3aapiho7Tgdxzg2_4LJJ43iiPiOcxg; datadome=miw1EGsFCSevYlR7KgjVI4BmFOypjcKyCmd0Y_gUeVq8~bPRgqXjx8O1J1pHYQjZQJcMfWtgjfDR3HjZxSA7mkw9kK_z~UvVs7zjIeZHovUSmUgGcYockpfluKRmF2AE; _gcl_aw=GCL.1756803720.null; OptanonConsent=isGpcEnabled=0&datestamp=Tue+Sep+02+2025+11%3A01%3A58+GMT%2B0200+(heure+d%E2%80%99%C3%A9t%C3%A9+d%E2%80%99Europe+centrale)&version=202405.2.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=fbbe8180-3e70-4d0b-96e7-914662726f4f&interactionCount=1&isAnonUser=1&landingPath=NotLandingPage&groups=C0001%3A1%2CC0004%3A1%2CC0002%3A1%2CC0003%3A1%2CV2STACK42%3A1&intType=1&geolocation=FR%3BPDL&AwaitingReconsent=false; TASession=%1%V2ID.F4CEF9CC70DC4DDEB61ED9D26E5415E5*SQ.34*PR.39538%7C*LS.Restaurant_Review*HS.recommended*ES.popularity*DS.5*SAS.popularity*FPS.oldFirst*FA.1*DF.0*TRA.true*LD.695212*EAU._; TASID=F4CEF9CC70DC4DDEB61ED9D26E5415E5; TATrkConsent=eyJvdXQiOiJTT0NJQUxfTUVESUEiLCJpbiI6IkFEVixBTkEsRlVOQ1RJT05BTCJ9; __eoi=ID=78867b8d1674a0a9:T=1756127268:RT=1756803561:S=AA-AfjYWP4spqQx7RXre2t3NrxsP; __gads=ID=30134c7f42076060:T=1756127268:RT=1756803561:S=ALNI_Ma4uu3yBi6_GLwttg0dEWmXe9kfew; __gpi=UID=00000f1b9488d1df:T=1756127268:RT=1756803561:S=ALNI_MYasBYYn2DOQOkbSfNREp7EqMwghA; _lr_sampling_rate=100; _lr_env_src_ats=false; _lr_retry_request=true; TAUD=LA-1756302055488-1*RDD-1-2025_08_27*RD-*********-2025_09_02.11950796*LG-*********-2.1.F.*LD-*********-.....; TAReturnTo=%1%%2FShowUserReviews-g187147-d695212-r1022065358-Bofinger-Paris_Ile_de_France.html; roybatty=TNI1625!AK7TS4MUMyN0jpU%2Frps8dFh0PCA11yCg4yGR%2FyN5oYUrnA09s6VQfDC%2FnQqaj9yUlBDIR5k5ec3TO7uhX1yEsJkxgr9PZP7LN3FwkcjGwFE58ftPuwwN8wTViuW%2FbZYeb7Md%2B04demKNXLxtgdd0rf6kvPGnrXtnjM9R5c1ZzJRAYdTll5QATv3aYWjk5eABEw%3D%3D%2C1; TATravelInfo=V2*A.2*MG.-1*HP.2*FL.3*DSM.1756802782416*RS.1*RY.2025*RM.9*RD.2*RH.20*RG.2; PAC=ALiNiXUvusH_2b0nOdUru-Ku9EArMxsRcMYWTvn8pcINEr6D5z8jlxB8QdezfMxHbQwECI-B3zQTEIU6xMb29D7tGlFEuWsFjXBewzxymtXmMwFsAYzZnHw_-Kyz5qZHIDHuZoLuNVJtvwXPi-cpflqm9fPilHXwfLDoQaZqFkZzMjRHPbKwAWGdX4X5_kDgTyHWN_EL260Yk_Ih-qvJfl0AIQTR13I2PaxOHZy0cVwQ2kndW_Cia6ZxmTrX91Jr6BbHefhtz1gXNqsu8DPxVz8%3D; PMC=V2*MS.46*MD.20250827*LD.20250902; SRT=TART_SYNC; TADCID=ongZUHGkEHMLcf0tABQCJ4S5rDsRRMescG99Hippfof7kFtJ4sMDJldOvm84JweeslsIodcF3ju_DAT9rIkDEhVDoJPLUWHkBCU; TART=%1%enc%3AV7GxQgIj8L%2BqzlAil%2FwPvhJYb61EW7h1zXyagSsMd3FFxd5Qxjf13RroqvgmTEKkW1JxtpsONWQ%3D; VRMCID=%1%V1*id.10568*llp.%2FRestaurant_Review-g187226-d2421607-Reviews-Mango_Bay-Hyeres_French_Riviera_Cote_d_Azur_Provence_Alpes_Cote_d_Azur%5C.html*e.1757324349873; TASameSite=1; G_AUTH2_MIGRATION=informational; ServerPool=C; _gcl_au=1.1.82871737.1756127267; OTAdditionalConsentString=1~***********.***********.***************.***************.***************.***************.259.266.286.291.311.318.320.322.323.327.367.371.385.394.407.415.424.430.436.445.486.491.494.495.522.523.540.550.559.560.568.574.576.584.587.591.737.802.803.820.839.864.899.904.922.931.938.959.979.981.985.1003.1027.1031.1040.1046.1051.1053.1067.1092.1095.1097.1099.1107.1109.1135.1143.1149.1152.1162.1166.1186.1188.1205.1215.1226.1227.1230.1252.1268.1270.1276.1284.1290.1301.1307.1312.1329.1345.1356.1375.1403.1415.1416.1421.1423.1440.1449.1455.1495.1512.1516.1525.1540.1548.1555.1558.1570.1577.1579.1583.1584.1603.1616.1638.1651.1653.1659.1667.1677.1678.1682.1697.1699.1703.1712.1716.1721.1725.1732.1745.1750.1765.1782.1786.1800.1810.1825.1827.1832.1838.1840.1842.1843.1845.1859.1866.1870.1878.1880.1889.1917.1929.1942.1944.1962.1963.1964.1967.1968.1969.1978.1985.1987.2003.2008.2027.2035.2039.2047.2052.2056.2064.2068.2072.2074.2088.2090.2103.2107.2109.2115.2124.2130.2133.2135.2137.2140.2147.2156.2166.2177.2186.2205.2213.2216.2219.2220.2222.2225.2234.2253.2275.2279.2282.2309.2312.2316.2322.2325.2328.2331.2335.2336.2343.2354.2358.2359.2370.2376.2377.2387.2400.2403.2405.2407.2411.2414.2416.2418.2425.2440.2447.2461.2465.2468.2472.2477.2481.2484.2486.2488.2493.2498.2501.2510.2517.2526.2527.2532.2535.2542.2552.2563.2564.2567.2568.2569.2571.2572.2575.2577.2583.2584.2596.2604.2605.2608.2609.2610.2612.2614.2621.2627.2628.2629.2633.2636.2642.2643.2645.2646.2650.2651.2652.2656.2657.2658.2660.2661.2669.2670.2677.2681.2684.2687.2690.2695.2698.2713.2714.2729.2739.2767.2768.2770.2772.2784.2787.2791.2792.2798.2801.2805.2812.2813.2816.2817.2821.2822.2827.2830.2831.2833.2834.2838.2839.2844.2846.2849.2850.2852.2854.2860.2862.2863.2865.2867.2869.2873.2874.2875.2876.2878.2880.2881.2882.2883.2884.2886.2887.2888.2889.2891.2893.2894.2895.2897.2898.2900.2901.2908.2909.2916.2917.2918.2919.2920.2922.2923.2927.2929.2930.2931.2940.2941.2947.2949.2950.2956.2958.2961.2963.2964.2965.2966.2968.2973.2975.2979.2980.2981.2983.2985.2986.2987.2994.2995.2997.2999.3000.3002.3003.3005.3008.3009.3010.3012.3016.3017.3018.3019.3028.3034.3038.3043.3052.3053.3055.3058.3059.3063.3066.3068.3070.3073.3074.3075.3076.3077.3089.3090.3093.3094.3095.3097.3099.3100.3106.3109.3112.3117.3119.3126.3127.3128.3130.3135.3136.3145.3150.3151.3154.3155.3163.3167.3172.3173.3182.3183.3184.3185.3187.3188.3189.3190.3194.3196.3209.3210.3211.3214.3215.3217.3222.3223.3225.3226.3227.3228.3230.3231.3234.3235.3236.3237.3238.3240.3244.3245.3250.3251.3253.3257.3260.3270.3272.3281.3288.3290.3292.3293.3296.3299.3300.3306.3307.3309.3314.3315.3316.3318.3324.3328.3330.3331.3531.3731.3831.4131.4531.4631.4731.4831.5231.6931.7235.7831.7931.8931.9731.10231.10631.10831.11031.11531.13632.13731.14034.14133.14237.14332.15731.16831.16931.21233.23031.25131.25731.25931.26031.26631.26831.27731.27831.28031.28731.28831.29631.32531.34231.34631.36831.39131.39531.40632.41131.41531.43631.43731.43831.45931; OptanonAlertBoxClosed=2025-08-25T13:07:46.565Z; eupubconsent-v2=CQWs3HAQWs3HAAcABBFRB5FsAP_gAEPgACiQLgtR_C5ebWli-TZUIbtkaYwP55gj4kQhBgaIkWwFwBOG7BgCB2EwNARYJiACGBAAkiDBAQNlHABUAQAAAIgRiSCMYEyEgTNKJKBAiFMRI0JYCBxmmoFDWQCY5kqsshc1mBeAAAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAAAAAAAA_YaffzPn__rl_e7X_vf_n37v943H77v____f_-6C4AAJgoVEEJYEAARKBhBAgAEEYQAUAAIAAEgKACAEAQBOQIAB1hMAAACAAEAAAAAIIAAQAACQAIRAAAAQCAAAAQCAAMAAAICAAgQAAQAWIAEAAIBoCAQEEAgGACBiFQaYEgAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD6MjTAMHzBMkpkGQBMEZGSbEJvwmHjkKIUEOQmxAKBACQAdABcAGyARAAwgCdAFyANsAgcEADAA6AFcARAAwgCdAIHBgA4AOgAuADZAIgAYQBcgEDhAAcAHQA2QCIAGEAToAuQCBwoAGAFwAwgEDhgAIAwgEDhwAYAHQBEADCAJ0AgcBFcgACAMIBA4kADAIgAYQCBxQAKADoAiABhAE6AQOAAA.f_wACHwAAAAA; pbjs_sharedId=4f251620-4355-4a07-bb25-47f92317108e; pbjs_sharedId_cst=Tiy4LDssdg%3D%3D; pbjs_unifiedID=%7B%22TDID_LOOKUP%22%3A%22FALSE%22%2C%22TDID_CREATED_AT%22%3A%222025-08-25T13%3A07%3A46%22%7D; pbjs_unifiedID_cst=Tiy4LDssdg%3D%3D; TASSK=enc%3AAFZXOe%2BJ5ytFQF2CXtGuEYGMjZXyZNc19Fxk63wqibbxSShNL9yBpxMEJ2NH8WzDUItUC4R3n5qf9sXxdSWhkpPhviLK%2BpSOY3g%2FxFosdNkcXdNmtuqyEJJuhzkFh7LjSg%3D%3D"

    }));
    return headers;
};

module.exports = {
    buildReviewGraphQlHeaders,
    buildProxyUrl,
    callPuppeteerService,
    checkMandatoryParams,
    cleanHtml,
    crawlPage,
    crawlPageBashGet,
    extractSocialIdFromEndpoint,
    levenshteinDistance,
    log,
    randomString,
    reverseString
};

function log(randomId, ...args) {
    if (randomId) {
        console.timeLog(randomId, ...args);
    }
}

/**
 * Temporary function using Promise.all (waiting for lambdas to use Node 16. and allow Promise.any in package-crawlers)
 * @param {Object} requestParams - example {url: 'http://xxxxx', headers: {User-Agent: 'xxxxx'}},
 * @param {Function} shouldWork
 * @param {Number} retries
 */
// eslint-disable-next-line complexity
const fetchUntilWorks = async (
    requestParams,
    shouldWork,
    retries,
    crawlersAmt,
    randomId
) => {
    if (!requestParams.url) {
        log(randomId, "no url");
        throw "Need to pass an url in the requestParams";
    }
    let responses;
    for (let i = 1; i <= retries; i += 1) {
        log(randomId, `try number ${i} of ${retries}`);
        try {
            const currentRequestParams =
                requestParams?.headers && Array.isArray(requestParams.headers)
                    ? {
                        ...requestParams,
                        headers:
                              requestParams.headers[
                                  i % requestParams.headers.length
                              ],
                    }
                    : requestParams;
            log(randomId, `Waiting for ${crawlersAmt} crawlers`);
            responses = await Promise.all(
                [...Array(crawlersAmt).keys()].map((index) =>
                    crawl(currentRequestParams, index)
                        .then((result) => {
                            if (result.data && shouldWork(result.data)) {
                                log(
                                    randomId,
                                    `Crawler ${index} sucess shouldwork`
                                );
                                return result.data;
                            }
                            log(randomId, `Crawler ${index} failed shouldwork`);
                            return {
                                error: true,
                                errorData: new Error(
                                    "Finished with no proper result"
                                ),
                            };
                        })
                        .catch((err) => {
                            log(randomId, `Crawler ${index} error`);
                            return { error: true, errorData: err };
                        })
                )
            );
            const correctResponses = responses.filter(
                (res) => res && !res.error
            );
            if (correctResponses.length === 0) {
                log(randomId, `No good responses`);
                throw responses[0].errorData;
            }
            log(randomId, `Returning correctResponses[0]`);
            return correctResponses[0];
        } catch (error) {
            log(randomId, `Restarting crawlers ...`);
            console.warn("[FETCH_UNTIL_WORKS] - Error", {
                error,
                currentTry: i,
                maxRetries: retries,
                url: requestParams.url,
            });
            await Promise.all(
                [...Array(crawlersAmt).keys()].map((index) =>
                    restart(index).catch((err) =>
                        console.warn(err.response?.data)
                    )
                )
            );
            log(randomId, `Done restarting crawlers ...`);
            if (i === retries) {
                log(randomId, `Last retry reached`);
                if (error && error.statusCode === 403) {
                    log(randomId, `Last retry reached: Error 403`);
                    throw error.error;
                } else if (String(error).match(/proper/)) {
                    log(randomId, `Last retry reached: No proper result`);
                    throw error;
                } else {
                    log(randomId, `Last retry reached: Unexpected Error`);
                    throw `Unexpected Error${error}`;
                }
            }
        }
    }
};

/**
 *
 * @param {Object} params {url, headers}
 * @param {Number} i
 */
const crawl = function (params, i) {
    return axios({
        method: "post",
        url: config.crawlers.baseUrl + i,
        headers: {
            "x-api-key": config.crawlers.apiKey,
        },
        data: params,
    });
};

/**
 *
 * @param {Number} i
 * Resets the platformsScrapper function (use when spotted by the website several times)
 */
const restart = function (i) {
    return axios({
        method: "post",
        url: `${config.crawlers.baseUrl}restart`,
        headers: {
            "x-api-key": config.crawlers.apiKey,
        },
        data: {
            number: String(i),
        },
        timeout: 4000,
    });
};


// index.js (entry point of lambda)

const config = require('./configuration');

/**
 * Main function of the lambda
 * Takes a POST request from the dispatcher and return
 * data
 *
 *
 * @param event<object> is the body of the request
 * {
 *     body:'<what ever is send>'
 * }
 * @param context
 * @param callback(err, res): response to the request
 *      res: {statusCode: <200|400>, headers: {}, body: <object>}
 */

exports.handler = async (event, _context) => {
    const { authorization } = event;
    if (!authorization || !isAuthorized(authorization)) {
        throw new Error('Not authorized');
    }
    const {
        platform, platformKey, pageType, params, restaurantId
    } = event.body;
    const { main } = platform
        ? require(`./platforms/${platform}/${pageType}`)
        : require(`./platforms/${platformKey}/${pageType}`);
    const result = await main(params, pageType, restaurantId, platformKey);
    // need promise.resolve for localstack to work properly
    return Promise.resolve(result);
};

const isAuthorized = (auth) => auth === config.authorization;

